<!DOCTYPE html>
<!--
    CLOUDFLARE IMAGE HOSTING SETUP - READY TO USE!
    ✅ Worker URL: https://image-uploader.mauricio-e1e.workers.dev
    ✅ Auth Key: your-super-secret-auth-key-2024 (make sure this matches your worker secret)
    ✅ R2 Domain: img.stokeleads.com

    Your form will now upload images to Cloudflare R2 instead of ImgBB!
-->
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Estimate Form</title>
    
    <!-- Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts: Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        /* Use the Inter font family */
        body {
            font-family: 'Inter', sans-serif;
        }
        /* Hide the default file input */
        input[type="file"] {
            display: none;
        }
        /* Simple spinner for loading state */
        .loader {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        /* Fix for Google Places dropdown styling */
        .pac-container {
            background-color: #FFF;
            z-index: 1000 !important;
            border-radius: 8px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
            border: 1px solid #DDD;
            font-family: 'Inter', sans-serif;
        }
        .pac-item {
            padding: 10px;
            font-size: 14px;
            cursor: pointer;
            border-top: 1px solid #DDD;
        }
        .pac-item:first-child {
            border-top: none;
        }
        .pac-item:hover {
            background-color: #f5f5f5;
        }
        .pac-item-query {
            font-weight: 600;
        }
    </style>
</head>
<body class="bg-gray-100 flex items-center justify-center min-h-screen p-4">

    <!-- Form Container -->
    <div class="w-full max-w-md bg-blue-600 text-white rounded-2xl shadow-2xl p-8">
        
        <h2 class="text-2xl font-bold text-center mb-6">Get Your Free Project Estimate</h2>

        <!-- The Form -->
        <form id="estimateForm">
            <div class="space-y-4">
                <!-- Form Fields -->
                <input type="text" name="first_name" placeholder="First Name" class="w-full p-3 rounded-lg text-gray-800" required>
                <input type="text" name="last_name" placeholder="Last Name" class="w-full p-3 rounded-lg text-gray-800" required>
                <input type="tel" name="phone" placeholder="Phone*" class="w-full p-3 rounded-lg text-gray-800" required>
                <input type="email" name="email" placeholder="Email*" class="w-full p-3 rounded-lg text-gray-800" required>
                
                <!-- Address Autocomplete Input -->
                <input id="address-autocomplete" type="text" placeholder="Search address" class="w-full p-3 rounded-lg text-gray-800">
                
                <!-- Hidden fields for structured address data -->
                <input type="hidden" id="address" name="address">
                <input type="hidden" id="city" name="city">
                <input type="hidden" id="state" name="state">
                <input type="hidden" id="country" name="country">
                <input type="hidden" id="postal_code" name="postal_code">

                <!-- Hidden field to store the uploaded image URL -->
                <input type="hidden" id="imageUrl" name="project_image_url">

                <!-- Image Upload Section -->
                <div id="imageUploadArea" class="bg-blue-500 border-2 border-dashed border-blue-300 rounded-lg p-6 text-center cursor-pointer">
                    <div id="uploadContent">
                         <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-10 w-10 text-blue-200" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <label for="fileUpload" class="font-medium text-white hover:text-blue-100 cursor-pointer mt-2 block">
                            Image Upload of Project
                        </label>
                        <p class="text-xs text-blue-200 mt-1">PNG, JPG, GIF up to 10MB</p>
                    </div>
                    <!-- This is the actual file input, it's hidden -->
                    <input id="fileUpload" type="file" accept="image/*">
                </div>
                
                <!-- Status message area for uploads -->
                <div id="uploadStatus" class="text-center text-sm"></div>

                <!-- Consent Checkbox -->
                <div class="flex items-start space-x-3">
                    <input id="consent" name="consent" type="checkbox" class="h-5 w-5 rounded mt-1" required>
                    <label for="consent" class="text-xs text-blue-100">
                        I agree to receive text message updates from Deckora. Msg frequency varies (e.g., up to 4/month). Msg & data rates may apply. Reply STOP to unsubscribe at any time. View privacy & terms.
                    </label>
                </div>
            </div>

            <!-- Submit Button -->
            <button type="submit" class="w-full bg-gray-800 hover:bg-gray-900 text-white font-bold p-4 rounded-lg mt-6 transition duration-300">
                Get Started
            </button>
            
            <!-- Form submission status -->
            <div id="formStatus" class="text-center mt-4 font-medium"></div>
        </form>
    </div>

    <script>
        // --- CONFIGURATION ---
        // Step 1: Your Cloudflare Worker URL for image uploads
        const CLOUDFLARE_WORKER_URL = 'https://image-uploader.mauricio-e1e.workers.dev';
        const CLOUDFLARE_AUTH_KEY = 'your-super-secret-auth-key-2024'; // The same secret you set in the worker

        // Step 2: Get your Workflow Webhook URL from HighLevel Automations (as before).
        const HIGHLEVEL_WEBHOOK_URL = 'https://services.leadconnectorhq.com/hooks/BK5WOlszHMZB0udM7qC1/webhook-trigger/01961497-8bf5-4d5b-9ce2-013a513f5de0';
        
        // Step 3: Get your Google Places API Key.
        // 1. Go to the Google Cloud Console (https://console.cloud.google.com/).
        // 2. Create a new project if you don't have one.
        // 3. Go to "APIs & Services" -> "Library" and enable "Places API" and "Maps JavaScript API".
        // 4. Go to "APIs & Services" -> "Credentials", create a new API Key.
        // 5. IMPORTANT: Restrict your key! For testing, you can leave it open, but for production, you should restrict it to your website's domain to prevent unauthorized use.
        const GOOGLE_PLACES_API_KEY = 'AIzaSyB8yL3lXuLVfXxniT62ulMUhDWksTCscTE';
        // --- END CONFIGURATION ---

        // Load the Google Places API script
        const googleApiScript = document.createElement('script');
        googleApiScript.src = `https://maps.googleapis.com/maps/api/js?key=${GOOGLE_PLACES_API_KEY}&libraries=places&callback=initAutocomplete`;
        googleApiScript.async = true;
        googleApiScript.defer = true;
        document.head.appendChild(googleApiScript);

        const fileUploadInput = document.getElementById('fileUpload');
        const imageUploadArea = document.getElementById('imageUploadArea');
        const uploadContent = document.getElementById('uploadContent');
        const uploadStatus = document.getElementById('uploadStatus');
        const imageUrlInput = document.getElementById('imageUrl');
        const estimateForm = document.getElementById('estimateForm');
        const formStatus = document.getElementById('formStatus');

        let autocomplete;

        // This function is called by the Google script once it's loaded
        function initAutocomplete() {
            const autocompleteInput = document.getElementById('address-autocomplete');
            if (!autocompleteInput) {
                console.error("Autocomplete input field not found");
                return;
            }
            autocomplete = new google.maps.places.Autocomplete(autocompleteInput, {
                types: ['address'],
                componentRestrictions: { 'country': ['US', 'CA'] }, // Restrict to US and Canada, for example
                fields: ['address_components', 'formatted_address']
            });
            autocomplete.addListener('place_changed', fillInAddress);
        }

        function fillInAddress() {
            const place = autocomplete.getPlace();
            
            // Clear previous address fields
            document.getElementById('address').value = '';
            document.getElementById('city').value = '';
            document.getElementById('state').value = '';
            document.getElementById('country').value = '';
            document.getElementById('postal_code').value = '';

            let streetNumber = '';
            let route = '';

            for (const component of place.address_components) {
                const componentType = component.types[0];
                switch (componentType) {
                    case "street_number":
                        streetNumber = component.long_name;
                        break;
                    case "route":
                        route = component.long_name;
                        break;
                    case "locality":
                        document.getElementById('city').value = component.long_name;
                        break;
                    case "administrative_area_level_1":
                        document.getElementById('state').value = component.short_name;
                        break;
                    case "country":
                        document.getElementById('country').value = component.long_name;
                        break;
                    case "postal_code":
                        document.getElementById('postal_code').value = component.long_name;
                        break;
                }
            }
            // Combine street number and route for the full street address
            document.getElementById('address').value = `${streetNumber} ${route}`.trim();
            
            console.log("Address populated:", {
                address: document.getElementById('address').value,
                city: document.getElementById('city').value,
                state: document.getElementById('state').value,
                country: document.getElementById('country').value,
                postal_code: document.getElementById('postal_code').value
            });
        }


        // Handle file selection
        fileUploadInput.addEventListener('change', (event) => {
            const file = event.target.files[0];
            if (file) {
                uploadImage(file);
            }
        });
        
        // Add drag and drop functionality
        imageUploadArea.addEventListener('dragover', (e) => { e.preventDefault(); imageUploadArea.classList.add('bg-blue-700'); });
        imageUploadArea.addEventListener('dragleave', (e) => { e.preventDefault(); imageUploadArea.classList.remove('bg-blue-700'); });
        imageUploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            imageUploadArea.classList.remove('bg-blue-700');
            const file = e.dataTransfer.files[0];
            if(file) uploadImage(file);
        });
        imageUploadArea.addEventListener('click', () => fileUploadInput.click());


        // Function to upload the image to Cloudflare Worker
        async function uploadImage(file) {
            if (!CLOUDFLARE_WORKER_URL) {
                uploadStatus.innerHTML = `<span class="text-red-300">Error: Cloudflare Worker URL is not set.</span>`;
                return;
            }
            if (!CLOUDFLARE_AUTH_KEY || CLOUDFLARE_AUTH_KEY === 'your-super-secret-auth-key-2024') {
                uploadStatus.innerHTML = `<span class="text-red-300">Error: Cloudflare auth key is not set.</span>`;
                return;
            }
            uploadStatus.innerHTML = '<div class="flex items-center justify-center gap-2">Uploading image... <div class="loader"></div></div>';
            const formData = new FormData();
            formData.append('image', file);
            try {
                const response = await fetch(CLOUDFLARE_WORKER_URL, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'Authorization': `Bearer ${CLOUDFLARE_AUTH_KEY}`
                    }
                });
                const result = await response.json();
                if (result.success) {
                    imageUrlInput.value = result.url;
                    // Create a thumbnail preview using the uploaded image URL
                    uploadContent.innerHTML = `<img src="${result.url}" alt="Image preview" class="mx-auto h-16 w-16 object-cover rounded-md">`;
                    uploadStatus.innerHTML = `<span class="text-green-200">Image uploaded!</span>`;
                } else {
                    throw new Error(result.error || 'Upload failed');
                }
            } catch (error) {
                console.error('Image upload failed:', error);
                uploadStatus.innerHTML = `<span class="text-red-300">Upload failed. Please try again.</span>`;
            }
        }

        // Handle form submission
        estimateForm.addEventListener('submit', async (event) => {
            event.preventDefault();
            if (!HIGHLEVEL_WEBHOOK_URL || HIGHLEVEL_WEBHOOK_URL === 'PASTE_YOUR_WORKFLOW_WEBHOOK_URL_HERE') {
                formStatus.innerHTML = `<span class="text-red-300">Error: HighLevel Webhook URL is not set.</span>`;
                return;
            }
            formStatus.innerHTML = 'Submitting...';
            const submitButton = estimateForm.querySelector('button[type="submit"]');
            submitButton.disabled = true;
            const formData = new FormData(estimateForm);
            const formPayload = Object.fromEntries(formData.entries());
            try {
                const response = await fetch(HIGHLEVEL_WEBHOOK_URL, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(formPayload),
                });
                if (response.ok) {
                    formStatus.innerHTML = `<span class="text-green-200">Thank you! Your estimate request has been sent.</span>`;
                    estimateForm.reset();
                    uploadContent.innerHTML = `
                         <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-10 w-10 text-blue-200" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1"><path stroke-linecap="round" stroke-linejoin="round" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg>
                        <label for="fileUpload" class="font-medium text-white hover:text-blue-100 cursor-pointer mt-2 block">Image Upload of Project</label>
                        <p class="text-xs text-blue-200 mt-1">PNG, JPG, GIF up to 10MB</p>`;
                    uploadStatus.innerHTML = '';
                } else { throw new Error(`Server responded with status: ${response.status}`); }
            } catch (error) {
                console.error('Form submission failed:', error);
                formStatus.innerHTML = `<span class="text-red-300">Submission failed. Please try again.</span>`;
            } finally {
                submitButton.disabled = false;
            }
        });
    </script>
</body>
</html>
