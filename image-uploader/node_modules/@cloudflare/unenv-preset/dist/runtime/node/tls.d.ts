export { CLIENT_RENEG_LIMIT, CLIENT_RENEG_WINDOW, createSecurePair, createServer, DEFAULT_CIPHERS, DEFAULT_ECDH_CURVE, DEFAULT_MAX_VERSION, DEFAULT_MIN_VERSION, getCiphers, rootCertificates, Server, } from "unenv/node/tls";
export declare const checkServerIdentity: typeof import("tls").checkServerIdentity, connect: typeof import("tls").connect, createSecureContext: typeof import("tls").createSecureContext, convertALPNProtocols: any, SecureContext: any, TLSSocket: typeof import("tls").TLSSocket;
declare const _default: any;
export default _default;
