{"$schema": "http://json-schema.org/draft-07/schema#", "$ref": "#/definitions/RawConfig", "definitions": {"RawConfig": {"type": "object", "additionalProperties": false, "properties": {"$schema": {"type": "string"}, "env": {"type": "object", "additionalProperties": {"$ref": "#/definitions/RawEnvironment"}, "description": "The `env` section defines overrides for the configuration for different environments.\n\nAll environment fields can be specified at the top level of the config indicating the default environment settings.\n\n- Some fields are inherited and overridable in each environment.\n- But some are not inherited and must be explicitly specified in every environment, if they are specified at the top level.\n\nFor more information, see the documentation at https://developers.cloudflare.com/workers/cli-wrangler/configuration#environments", "default": {}}, "name": {"type": "string", "description": "The name of your Worker. Alphanumeric + dashes only."}, "account_id": {"type": "string", "description": "This is the ID of the account associated with your zone. You might have more than one account, so make sure to use the ID of the account associated with the zone/route you provide, if you provide one. It can also be specified through the CLOUDFLARE_ACCOUNT_ID environment variable."}, "compatibility_date": {"type": "string", "description": "A date in the form yyyy-mm-dd, which will be used to determine which version of the Workers runtime is used.\n\nMore details at https://developers.cloudflare.com/workers/configuration/compatibility-dates"}, "compatibility_flags": {"type": "array", "items": {"type": "string"}, "description": "A list of flags that enable features from upcoming features of the Workers runtime, usually used together with compatibility_date.\n\nMore details at https://developers.cloudflare.com/workers/configuration/compatibility-flags/", "default": []}, "main": {"type": "string", "description": "The entrypoint/path to the JavaScript file that will be executed."}, "find_additional_modules": {"type": "boolean", "description": "If true then <PERSON><PERSON><PERSON> will traverse the file tree below `base_dir`; Any files that match `rules` will be included in the deployed Worker. Defaults to true if `no_bundle` is true, otherwise false."}, "preserve_file_names": {"type": "boolean", "description": "Determines whether <PERSON><PERSON><PERSON> will preserve bundled file names. Defaults to false. If left unset, files will be named using the pattern ${fileHash}-${basename}, for example, `34de60b44167af5c5a709e62a4e20c4f18c9e3b6-favicon.ico`."}, "base_dir": {"type": "string", "description": "The directory in which module rules should be evaluated when including additional files into a Worker deployment. This defaults to the directory containing the `main` entry point of the Worker if not specified."}, "workers_dev": {"type": "boolean", "description": "Whether we use <name>.<subdomain>.workers.dev to test and deploy your Worker.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#workersdev", "default": true}, "preview_urls": {"type": "boolean", "description": "Whether we use <version>-<name>.<subdomain>.workers.dev to serve Preview URLs for your Worker.", "default": true}, "routes": {"type": "array", "items": {"$ref": "#/definitions/Route"}, "description": "A list of routes that your Worker should be published to. Only one of `routes` or `route` is required.\n\nOnly required when workers_dev is false, and there's no scheduled Worker (see `triggers`)\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#types-of-routes"}, "route": {"$ref": "#/definitions/Route", "description": "A route that your Worker should be published to. Literally the same as routes, but only one. Only one of `routes` or `route` is required.\n\nOnly required when workers_dev is false, and there's no scheduled Worker"}, "tsconfig": {"type": "string", "description": "Path to a custom tsconfig"}, "jsx_factory": {"type": "string", "description": "The function to use to replace jsx syntax.", "default": "React.createElement"}, "jsx_fragment": {"type": "string", "description": "The function to use to replace jsx fragment syntax.", "default": "React.Fragment"}, "migrations": {"type": "array", "items": {"$ref": "#/definitions/DurableObjectMigration"}, "description": "A list of migrations that should be uploaded with your Worker.\n\nThese define changes in your Durable Object declarations.\n\nMore details at https://developers.cloudflare.com/workers/learning/using-durable-objects#configuring-durable-object-classes-with-migrations", "default": []}, "triggers": {"type": "object", "properties": {"crons": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false, "description": "\"Cron\" definitions to trigger a Worker's \"scheduled\" function.\n\nLets you call Workers periodically, much like a cron job.\n\nMore details here https://developers.cloudflare.com/workers/platform/cron-triggers\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#triggers", "default": "{crons: undefined}"}, "limits": {"$ref": "#/definitions/UserLimits", "description": "Specify limits for runtime behavior. Only supported for the \"standard\" Usage Model\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#limits"}, "rules": {"type": "array", "items": {"$ref": "#/definitions/Rule"}, "description": "An ordered list of rules that define which modules to import, and what type to import them as. You will need to specify rules to use Text, Data, and CompiledWasm modules, or when you wish to have a .js file be treated as an ESModule instead of CommonJS.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#bundling"}, "build": {"type": "object", "properties": {"command": {"type": "string", "description": "The command used to build your Worker. On Linux and macOS, the command is executed in the `sh` shell and the `cmd` shell for Windows. The `&&` and `||` shell operators may be used."}, "cwd": {"type": "string", "description": "The directory in which the command is executed."}, "watch_dir": {"anyOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}], "description": "The directory to watch for changes while using wrangler dev, defaults to the current working directory"}}, "additionalProperties": false, "description": "Configures a custom build step to be run by <PERSON>rang<PERSON> when building your Worker.\n\nRefer to the [custom builds documentation](https://developers.cloudflare.com/workers/cli-wrangler/configuration#build) for more details.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#custom-builds", "default": {"watch_dir": "./src"}}, "no_bundle": {"type": "boolean", "description": "Skip internal build steps and directly deploy script"}, "minify": {"type": "boolean", "description": "Minify the script before uploading."}, "keep_names": {"type": "boolean", "description": "Set the `name` property to the original name for functions and classes renamed during minification.\n\nSee https://esbuild.github.io/api/#keep-names", "default": true}, "first_party_worker": {"type": "boolean", "description": "Designates this Worker as an internal-only \"first-party\" Worker."}, "logfwdr": {"type": "object", "properties": {"bindings": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "The binding name used to refer to logfwdr"}, "destination": {"type": "string", "description": "The destination for this logged message"}}, "required": ["name", "destination"], "additionalProperties": false}}}, "required": ["bindings"], "additionalProperties": false, "description": "List of bindings that you will send to logfwdr", "default": {"bindings": []}}, "logpush": {"type": "boolean", "description": "Send Trace Events from this Worker to Workers Logpush.\n\nThis will not configure a corresponding Logpush job automatically.\n\nFor more information about Workers Logpush, see: https://blog.cloudflare.com/logpush-for-workers/"}, "upload_source_maps": {"type": "boolean", "description": "Include source maps when uploading this worker.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#source-maps"}, "placement": {"type": "object", "properties": {"mode": {"type": "string", "enum": ["off", "smart"]}, "hint": {"type": "string"}}, "required": ["mode"], "additionalProperties": false, "description": "Specify how the Worker should be located to minimize round-trip time.\n\nMore details: https://developers.cloudflare.com/workers/platform/smart-placement/"}, "assets": {"$ref": "#/definitions/Assets", "description": "Specify the directory of static assets to deploy/serve\n\nMore details at https://developers.cloudflare.com/workers/frameworks/\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#assets"}, "observability": {"$ref": "#/definitions/Observability", "description": "Specify the observability behavior of the Worker.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#observability"}, "compliance_region": {"type": "string", "enum": ["public", "fedramp_high"], "description": "Specify the compliance region mode of the Worker.\n\nAlthough if the user does not specify a compliance region, the default is `public`, it can be set to `undefined` in configuration to delegate to the CLOUDFLARE_COMPLIANCE_REGION environment variable."}, "define": {"type": "object", "additionalProperties": {"type": "string"}, "description": "A map of values to substitute when deploying your Worker.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "default": {}}, "vars": {"type": "object", "additionalProperties": {"anyOf": [{"type": "string"}, {"$ref": "#/definitions/Json"}]}, "description": "A map of environment variables to set when deploying your Worker.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#environment-variables", "default": {}}, "durable_objects": {"type": "object", "properties": {"bindings": {"$ref": "#/definitions/DurableObjectBindings"}}, "required": ["bindings"], "additionalProperties": false, "description": "A list of durable objects that your Worker should be bound to.\n\nFor more information about Durable Objects, see the documentation at https://developers.cloudflare.com/workers/learning/using-durable-objects\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#durable-objects", "default": {"bindings": []}}, "workflows": {"type": "array", "items": {"$ref": "#/definitions/WorkflowBinding"}, "description": "A list of workflows that your Worker should be bound to.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "default": []}, "cloudchamber": {"$ref": "#/definitions/CloudchamberConfig", "description": "Cloudchamber configuration\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "default": {}}, "containers": {"type": "array", "items": {"$ref": "#/definitions/ContainerApp"}, "description": "Container related configuration\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "default": []}, "kv_namespaces": {"type": "array", "items": {"type": "object", "properties": {"binding": {"type": "string", "description": "The binding name used to refer to the KV Namespace"}, "id": {"type": "string", "description": "The ID of the KV namespace"}, "preview_id": {"type": "string", "description": "The ID of the KV namespace used during `wrangler dev`"}, "experimental_remote": {"type": "boolean", "description": "Whether the KV namespace should be remote or not (only available under `--x-remote-bindings`)"}}, "required": ["binding"], "additionalProperties": false}, "description": "These specify any Workers KV Namespaces you want to access from inside your Worker.\n\nTo learn more about KV Namespaces, see the documentation at https://developers.cloudflare.com/workers/learning/how-kv-works\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#kv-namespaces", "default": []}, "send_email": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "The binding name used to refer to the this binding"}, "destination_address": {"type": "string", "description": "If this binding should be restricted to a specific verified address"}, "allowed_destination_addresses": {"type": "array", "items": {"type": "string"}, "description": "If this binding should be restricted to a set of verified addresses"}}, "required": ["name"], "additionalProperties": false}, "description": "These specify bindings to send email from inside your Worker.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#email-bindings", "default": []}, "queues": {"type": "object", "properties": {"producers": {"type": "array", "items": {"type": "object", "properties": {"binding": {"type": "string", "description": "The binding name used to refer to the Queue in the Worker."}, "queue": {"type": "string", "description": "The name of this Queue."}, "delivery_delay": {"type": "number", "description": "The number of seconds to wait before delivering a message"}, "experimental_remote": {"type": "boolean", "description": "Whether the Queue producer should be remote or not (only available under `--x-remote-bindings`)"}}, "required": ["binding", "queue"], "additionalProperties": false}, "description": "Producer bindings"}, "consumers": {"type": "array", "items": {"type": "object", "properties": {"queue": {"type": "string", "description": "The name of the queue from which this consumer should consume."}, "type": {"type": "string", "description": "The consumer type, e.g., worker, http-pull, r2-bucket, etc. De<PERSON><PERSON> is worker."}, "max_batch_size": {"type": "number", "description": "The maximum number of messages per batch"}, "max_batch_timeout": {"type": "number", "description": "The maximum number of seconds to wait to fill a batch with messages."}, "max_retries": {"type": "number", "description": "The maximum number of retries for each message."}, "dead_letter_queue": {"type": "string", "description": "The queue to send messages that failed to be consumed."}, "max_concurrency": {"type": ["number", "null"], "description": "The maximum number of concurrent consumer Worker invocations. Leaving this unset will allow your consumer to scale to the maximum concurrency needed to keep up with the message backlog."}, "visibility_timeout_ms": {"type": "number", "description": "The number of milliseconds to wait for pulled messages to become visible again"}, "retry_delay": {"type": "number", "description": "The number of seconds to wait before retrying a message"}}, "required": ["queue"], "additionalProperties": false}, "description": "Consumer configuration"}}, "additionalProperties": false, "description": "Specifies Queues that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#queues", "default": {"consumers": [], "producers": []}}, "r2_buckets": {"type": "array", "items": {"type": "object", "properties": {"binding": {"type": "string", "description": "The binding name used to refer to the R2 bucket in the Worker."}, "bucket_name": {"type": "string", "description": "The name of this R2 bucket at the edge."}, "preview_bucket_name": {"type": "string", "description": "The preview name of this R2 bucket at the edge."}, "jurisdiction": {"type": "string", "description": "The jurisdiction that the bucket exists in. Default if not present."}, "experimental_remote": {"type": "boolean", "description": "Whether the R2 bucket should be remote or not (only available under `--x-remote-bindings`)"}}, "required": ["binding"], "additionalProperties": false}, "description": "Specifies R2 buckets that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#r2-buckets", "default": []}, "d1_databases": {"type": "array", "items": {"type": "object", "properties": {"binding": {"type": "string", "description": "The binding name used to refer to the D1 database in the Worker."}, "database_name": {"type": "string", "description": "The name of this D1 database."}, "database_id": {"type": "string", "description": "The UUID of this D1 database (not required)."}, "preview_database_id": {"type": "string", "description": "The UUID of this D1 database for Wrangler Dev (if specified)."}, "migrations_table": {"type": "string", "description": "The name of the migrations table for this D1 database (defaults to 'd1_migrations')."}, "migrations_dir": {"type": "string", "description": "The path to the directory of migrations for this D1 database (defaults to './migrations')."}, "database_internal_env": {"type": "string", "description": "Internal use only."}, "experimental_remote": {"type": "boolean", "description": "Whether the D1 database should be remote or not (only available under `--x-remote-bindings`)"}}, "required": ["binding"], "additionalProperties": false}, "description": "Specifies D1 databases that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#d1-databases", "default": []}, "vectorize": {"type": "array", "items": {"type": "object", "properties": {"binding": {"type": "string", "description": "The binding name used to refer to the Vectorize index in the Worker."}, "index_name": {"type": "string", "description": "The name of the index."}, "experimental_remote": {"type": "boolean", "description": "Whether the Vectorize index should be remote or not (only available under `--x-remote-bindings`)"}}, "required": ["binding", "index_name"], "additionalProperties": false}, "description": "Specifies Vectorize indexes that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#vectorize-indexes", "default": []}, "hyperdrive": {"type": "array", "items": {"type": "object", "properties": {"binding": {"type": "string", "description": "The binding name used to refer to the project in the Worker."}, "id": {"type": "string", "description": "The id of the database."}, "localConnectionString": {"type": "string", "description": "The local database connection string for `wrangler dev`"}}, "required": ["binding", "id"], "additionalProperties": false}, "description": "Specifies Hyperdrive configs that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#hyperdrive", "default": []}, "services": {"type": "array", "items": {"type": "object", "properties": {"binding": {"type": "string", "description": "The binding name used to refer to the bound service."}, "service": {"type": "string", "description": "The name of the service."}, "environment": {"type": "string", "description": "The environment of the service (e.g. production, staging, etc)."}, "entrypoint": {"type": "string", "description": "Optionally, the entrypoint (named export) of the service to bind to."}, "props": {"type": "object", "additionalProperties": {}, "description": "Optional properties that will be made available to the service via ctx.props."}, "experimental_remote": {"type": "boolean", "description": "Whether the service binding should be remote or not (only available under `--x-remote-bindings`)"}}, "required": ["binding", "service"], "additionalProperties": false}, "description": "Specifies service bindings (Worker-to-Worker) that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#service-bindings", "default": []}, "analytics_engine_datasets": {"type": "array", "items": {"type": "object", "properties": {"binding": {"type": "string", "description": "The binding name used to refer to the dataset in the Worker."}, "dataset": {"type": "string", "description": "The name of this dataset to write to."}}, "required": ["binding"], "additionalProperties": false}, "description": "Specifies analytics engine datasets that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#analytics-engine-datasets", "default": []}, "browser": {"type": "object", "properties": {"binding": {"type": "string"}, "experimental_remote": {"type": "boolean", "description": "Whether the Browser binding should be remote or not (only available under `--x-remote-bindings`)"}}, "required": ["binding"], "additionalProperties": false, "description": "A browser that will be usable from the Worker.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#browser-rendering", "default": {}}, "ai": {"type": "object", "properties": {"binding": {"type": "string"}, "staging": {"type": "boolean"}, "experimental_remote": {"type": "boolean", "description": "Whether the AI binding should be remote or not (only available under `--x-remote-bindings`)"}}, "required": ["binding"], "additionalProperties": false, "description": "Binding to the AI project.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#workers-ai", "default": {}}, "images": {"type": "object", "properties": {"binding": {"type": "string"}, "experimental_remote": {"type": "boolean", "description": "Whether the Images binding should be remote or not (only available under `--x-remote-bindings`)"}}, "required": ["binding"], "additionalProperties": false, "description": "Binding to Cloudflare Images\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#images", "default": {}}, "version_metadata": {"type": "object", "properties": {"binding": {"type": "string"}}, "required": ["binding"], "additionalProperties": false, "description": "Binding to the Worker Version's metadata"}, "unsafe": {"type": "object", "properties": {"bindings": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}}, "required": ["name", "type"], "additionalProperties": {}}, "description": "A set of bindings that should be put into a Worker's upload metadata without changes. These can be used to implement bindings for features that haven't released and aren't supported directly by wrangler or miniflare."}, "metadata": {"type": "object", "additionalProperties": {}, "description": "Arbitrary key/value pairs that will be included in the uploaded metadata.  Values specified here will always be applied to metadata last, so can add new or override existing fields."}, "capnp": {"anyOf": [{"type": "object", "properties": {"base_path": {"type": "string"}, "source_schemas": {"type": "array", "items": {"type": "string"}}}, "required": ["base_path", "source_schemas"], "additionalProperties": false}, {"type": "object", "properties": {"compiled_schema": {"type": "string"}}, "required": ["compiled_schema"], "additionalProperties": false}], "description": "Used for internal capnp uploads for the Workers runtime"}}, "additionalProperties": false, "description": "\"Unsafe\" tables for features that aren't directly supported by wrangler.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "default": {}}, "mtls_certificates": {"type": "array", "items": {"type": "object", "properties": {"binding": {"type": "string", "description": "The binding name used to refer to the certificate in the Worker"}, "certificate_id": {"type": "string", "description": "The uuid of the uploaded mTLS certificate"}, "experimental_remote": {"type": "boolean", "description": "Whether the mtls fetcher should be remote or not (only available under `--x-remote-bindings`)"}}, "required": ["binding", "certificate_id"], "additionalProperties": false}, "description": "Specifies a list of mTLS certificates that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#mtls-certificates", "default": []}, "tail_consumers": {"type": "array", "items": {"$ref": "#/definitions/TailConsumer"}, "description": "Specifies a list of Tail Workers that are bound to this Worker environment\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "default": []}, "dispatch_namespaces": {"type": "array", "items": {"type": "object", "properties": {"binding": {"type": "string", "description": "The binding name used to refer to the bound service."}, "namespace": {"type": "string", "description": "The namespace to bind to."}, "outbound": {"$ref": "#/definitions/DispatchNamespaceOutbound", "description": "Details about the outbound Worker which will handle outbound requests from your namespace"}, "experimental_remote": {"type": "boolean", "description": "Whether the Dispatch Namespace should be remote or not (only available under `--x-remote-bindings`)"}}, "required": ["binding", "namespace"], "additionalProperties": false}, "description": "Specifies namespace bindings that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#dispatch-namespace-bindings-workers-for-platforms", "default": []}, "pipelines": {"type": "array", "items": {"type": "object", "properties": {"binding": {"type": "string", "description": "The binding name used to refer to the bound service."}, "pipeline": {"type": "string", "description": "Name of the Pipeline to bind"}}, "required": ["binding", "pipeline"], "additionalProperties": false}, "description": "Specifies list of Pipelines bound to this Worker environment\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "default": []}, "secrets_store_secrets": {"type": "array", "items": {"type": "object", "properties": {"binding": {"type": "string", "description": "The binding name used to refer to the bound service."}, "store_id": {"type": "string", "description": "Id of the secret store"}, "secret_name": {"type": "string", "description": "Name of the secret"}}, "required": ["binding", "store_id", "secret_name"], "additionalProperties": false}, "description": "Specifies Secret Store bindings that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "default": []}, "unsafe_hello_world": {"type": "array", "items": {"type": "object", "properties": {"binding": {"type": "string", "description": "The binding name used to refer to the bound service."}, "enable_timer": {"type": "boolean", "description": "Whether the timer is enabled"}}, "required": ["binding"], "additionalProperties": false}, "description": "**DO NOT USE**. Hello World Binding Config to serve as an explanatory example.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "default": []}, "pages_build_output_dir": {"type": "string", "description": "The directory of static assets to serve.\n\nThe presence of this field in a Wrangler configuration file indicates a Pages project, and will prompt the handling of the configuration file according to the Pages-specific validation rules."}, "legacy_env": {"type": "boolean", "description": "A boolean to enable \"legacy\" style wrangler environments (from Wrangler v1). These have been superseded by Services, but there may be projects that won't (or can't) use them. If you're using a legacy environment, you can set this to `true` to enable it."}, "send_metrics": {"type": "boolean", "description": "Whether Wrangler should send usage metrics to Cloudflare for this project.\n\nWhen defined this will override any user settings. Otherwise, Wrangler will use the user's preference."}, "dev": {"$ref": "#/definitions/RawDevConfig", "description": "Options to configure the development server that your worker will use.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#local-development-settings"}, "site": {"type": "object", "properties": {"bucket": {"type": "string", "description": "The directory containing your static assets.\n\nIt must be a path relative to your Wrangler configuration file. Example: bucket = \"./public\"\n\nIf there is a `site` field then it must contain this `bucket` field."}, "entry-point": {"type": "string", "description": "The location of your Worker script.", "deprecated": "DO NOT use this (it's a holdover from Wrangler v1.x). Either use the top level `main` field, or pass the path to your entry file as a command line argument."}, "include": {"type": "array", "items": {"type": "string"}, "description": "An exclusive list of .gitignore-style patterns that match file or directory names from your bucket location. Only matched items will be uploaded. Example: include = [\"upload_dir\"]", "default": []}, "exclude": {"type": "array", "items": {"type": "string"}, "description": "A list of .gitignore-style patterns that match files or directories in your bucket that should be excluded from uploads. Example: exclude = [\"ignore_dir\"]", "default": []}}, "required": ["bucket"], "additionalProperties": false, "description": "The definition of a Worker Site, a feature that lets you upload static assets with your Worker.\n\nMore details at https://developers.cloudflare.com/workers/platform/sites\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#workers-sites"}, "wasm_modules": {"type": "object", "additionalProperties": {"type": "string"}, "description": "A list of wasm modules that your worker should be bound to. This is the \"legacy\" way of binding to a wasm module. ES module workers should do proper module imports."}, "text_blobs": {"type": "object", "additionalProperties": {"type": "string"}, "description": "A list of text files that your worker should be bound to. This is the \"legacy\" way of binding to a text file. ES module workers should do proper module imports."}, "data_blobs": {"type": "object", "additionalProperties": {"type": "string"}, "description": "A list of data files that your worker should be bound to. This is the \"legacy\" way of binding to a data file. ES module workers should do proper module imports."}, "alias": {"type": "object", "additionalProperties": {"type": "string"}, "description": "A map of module aliases. Lets you swap out a module for any others. Corresponds with esbuild's `alias` config\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#module-aliasing"}, "keep_vars": {"type": "boolean", "description": "By default, the Wrangler configuration file is the source of truth for your environment configuration, like a terraform file.\n\nIf you change your vars in the dashboard, wrangler *will* override/delete them on its next deploy.\n\nIf you want to keep your dashboard vars when wrangler deploys, set this field to true.", "default": false}}}, "RawEnvironment": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of your Worker. Alphanumeric + dashes only."}, "account_id": {"type": "string", "description": "This is the ID of the account associated with your zone. You might have more than one account, so make sure to use the ID of the account associated with the zone/route you provide, if you provide one. It can also be specified through the CLOUDFLARE_ACCOUNT_ID environment variable."}, "compatibility_date": {"type": "string", "description": "A date in the form yyyy-mm-dd, which will be used to determine which version of the Workers runtime is used.\n\nMore details at https://developers.cloudflare.com/workers/configuration/compatibility-dates"}, "compatibility_flags": {"type": "array", "items": {"type": "string"}, "description": "A list of flags that enable features from upcoming features of the Workers runtime, usually used together with compatibility_date.\n\nMore details at https://developers.cloudflare.com/workers/configuration/compatibility-flags/", "default": []}, "main": {"type": "string", "description": "The entrypoint/path to the JavaScript file that will be executed."}, "find_additional_modules": {"type": "boolean", "description": "If true then <PERSON><PERSON><PERSON> will traverse the file tree below `base_dir`; Any files that match `rules` will be included in the deployed Worker. Defaults to true if `no_bundle` is true, otherwise false."}, "preserve_file_names": {"type": "boolean", "description": "Determines whether <PERSON><PERSON><PERSON> will preserve bundled file names. Defaults to false. If left unset, files will be named using the pattern ${fileHash}-${basename}, for example, `34de60b44167af5c5a709e62a4e20c4f18c9e3b6-favicon.ico`."}, "base_dir": {"type": "string", "description": "The directory in which module rules should be evaluated when including additional files into a Worker deployment. This defaults to the directory containing the `main` entry point of the Worker if not specified."}, "workers_dev": {"type": "boolean", "description": "Whether we use <name>.<subdomain>.workers.dev to test and deploy your Worker.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#workersdev", "default": true}, "preview_urls": {"type": "boolean", "description": "Whether we use <version>-<name>.<subdomain>.workers.dev to serve Preview URLs for your Worker.", "default": true}, "routes": {"type": "array", "items": {"$ref": "#/definitions/Route"}, "description": "A list of routes that your Worker should be published to. Only one of `routes` or `route` is required.\n\nOnly required when workers_dev is false, and there's no scheduled Worker (see `triggers`)\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#types-of-routes"}, "route": {"$ref": "#/definitions/Route", "description": "A route that your Worker should be published to. Literally the same as routes, but only one. Only one of `routes` or `route` is required.\n\nOnly required when workers_dev is false, and there's no scheduled Worker"}, "tsconfig": {"type": "string", "description": "Path to a custom tsconfig"}, "jsx_factory": {"type": "string", "description": "The function to use to replace jsx syntax.", "default": "React.createElement"}, "jsx_fragment": {"type": "string", "description": "The function to use to replace jsx fragment syntax.", "default": "React.Fragment"}, "migrations": {"type": "array", "items": {"$ref": "#/definitions/DurableObjectMigration"}, "description": "A list of migrations that should be uploaded with your Worker.\n\nThese define changes in your Durable Object declarations.\n\nMore details at https://developers.cloudflare.com/workers/learning/using-durable-objects#configuring-durable-object-classes-with-migrations", "default": []}, "triggers": {"type": "object", "properties": {"crons": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false, "description": "\"Cron\" definitions to trigger a Worker's \"scheduled\" function.\n\nLets you call Workers periodically, much like a cron job.\n\nMore details here https://developers.cloudflare.com/workers/platform/cron-triggers\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#triggers", "default": "{crons: undefined}"}, "limits": {"$ref": "#/definitions/UserLimits", "description": "Specify limits for runtime behavior. Only supported for the \"standard\" Usage Model\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#limits"}, "rules": {"type": "array", "items": {"$ref": "#/definitions/Rule"}, "description": "An ordered list of rules that define which modules to import, and what type to import them as. You will need to specify rules to use Text, Data, and CompiledWasm modules, or when you wish to have a .js file be treated as an ESModule instead of CommonJS.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#bundling"}, "build": {"type": "object", "properties": {"command": {"type": "string", "description": "The command used to build your Worker. On Linux and macOS, the command is executed in the `sh` shell and the `cmd` shell for Windows. The `&&` and `||` shell operators may be used."}, "cwd": {"type": "string", "description": "The directory in which the command is executed."}, "watch_dir": {"anyOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}], "description": "The directory to watch for changes while using wrangler dev, defaults to the current working directory"}}, "additionalProperties": false, "description": "Configures a custom build step to be run by <PERSON>rang<PERSON> when building your Worker.\n\nRefer to the [custom builds documentation](https://developers.cloudflare.com/workers/cli-wrangler/configuration#build) for more details.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#custom-builds", "default": {"watch_dir": "./src"}}, "no_bundle": {"type": "boolean", "description": "Skip internal build steps and directly deploy script"}, "minify": {"type": "boolean", "description": "Minify the script before uploading."}, "keep_names": {"type": "boolean", "description": "Set the `name` property to the original name for functions and classes renamed during minification.\n\nSee https://esbuild.github.io/api/#keep-names", "default": true}, "first_party_worker": {"type": "boolean", "description": "Designates this Worker as an internal-only \"first-party\" Worker."}, "logfwdr": {"type": "object", "properties": {"bindings": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "The binding name used to refer to logfwdr"}, "destination": {"type": "string", "description": "The destination for this logged message"}}, "required": ["name", "destination"], "additionalProperties": false}}}, "required": ["bindings"], "additionalProperties": false, "description": "List of bindings that you will send to logfwdr", "default": {"bindings": []}}, "logpush": {"type": "boolean", "description": "Send Trace Events from this Worker to Workers Logpush.\n\nThis will not configure a corresponding Logpush job automatically.\n\nFor more information about Workers Logpush, see: https://blog.cloudflare.com/logpush-for-workers/"}, "upload_source_maps": {"type": "boolean", "description": "Include source maps when uploading this worker.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#source-maps"}, "placement": {"type": "object", "properties": {"mode": {"type": "string", "enum": ["off", "smart"]}, "hint": {"type": "string"}}, "required": ["mode"], "additionalProperties": false, "description": "Specify how the Worker should be located to minimize round-trip time.\n\nMore details: https://developers.cloudflare.com/workers/platform/smart-placement/"}, "assets": {"$ref": "#/definitions/Assets", "description": "Specify the directory of static assets to deploy/serve\n\nMore details at https://developers.cloudflare.com/workers/frameworks/\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#assets"}, "observability": {"$ref": "#/definitions/Observability", "description": "Specify the observability behavior of the Worker.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#observability"}, "compliance_region": {"type": "string", "enum": ["public", "fedramp_high"], "description": "Specify the compliance region mode of the Worker.\n\nAlthough if the user does not specify a compliance region, the default is `public`, it can be set to `undefined` in configuration to delegate to the CLOUDFLARE_COMPLIANCE_REGION environment variable."}, "define": {"type": "object", "additionalProperties": {"type": "string"}, "description": "A map of values to substitute when deploying your Worker.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "default": {}}, "vars": {"type": "object", "additionalProperties": {"anyOf": [{"type": "string"}, {"$ref": "#/definitions/Json"}]}, "description": "A map of environment variables to set when deploying your Worker.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#environment-variables", "default": {}}, "durable_objects": {"type": "object", "properties": {"bindings": {"$ref": "#/definitions/DurableObjectBindings"}}, "required": ["bindings"], "additionalProperties": false, "description": "A list of durable objects that your Worker should be bound to.\n\nFor more information about Durable Objects, see the documentation at https://developers.cloudflare.com/workers/learning/using-durable-objects\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#durable-objects", "default": {"bindings": []}}, "workflows": {"type": "array", "items": {"$ref": "#/definitions/WorkflowBinding"}, "description": "A list of workflows that your Worker should be bound to.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "default": []}, "cloudchamber": {"$ref": "#/definitions/CloudchamberConfig", "description": "Cloudchamber configuration\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "default": {}}, "containers": {"type": "array", "items": {"$ref": "#/definitions/ContainerApp"}, "description": "Container related configuration\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "default": []}, "kv_namespaces": {"type": "array", "items": {"type": "object", "properties": {"binding": {"type": "string", "description": "The binding name used to refer to the KV Namespace"}, "id": {"type": "string", "description": "The ID of the KV namespace"}, "preview_id": {"type": "string", "description": "The ID of the KV namespace used during `wrangler dev`"}, "experimental_remote": {"type": "boolean", "description": "Whether the KV namespace should be remote or not (only available under `--x-remote-bindings`)"}}, "required": ["binding"], "additionalProperties": false}, "description": "These specify any Workers KV Namespaces you want to access from inside your Worker.\n\nTo learn more about KV Namespaces, see the documentation at https://developers.cloudflare.com/workers/learning/how-kv-works\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#kv-namespaces", "default": []}, "send_email": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "The binding name used to refer to the this binding"}, "destination_address": {"type": "string", "description": "If this binding should be restricted to a specific verified address"}, "allowed_destination_addresses": {"type": "array", "items": {"type": "string"}, "description": "If this binding should be restricted to a set of verified addresses"}}, "required": ["name"], "additionalProperties": false}, "description": "These specify bindings to send email from inside your Worker.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#email-bindings", "default": []}, "queues": {"type": "object", "properties": {"producers": {"type": "array", "items": {"type": "object", "properties": {"binding": {"type": "string", "description": "The binding name used to refer to the Queue in the Worker."}, "queue": {"type": "string", "description": "The name of this Queue."}, "delivery_delay": {"type": "number", "description": "The number of seconds to wait before delivering a message"}, "experimental_remote": {"type": "boolean", "description": "Whether the Queue producer should be remote or not (only available under `--x-remote-bindings`)"}}, "required": ["binding", "queue"], "additionalProperties": false}, "description": "Producer bindings"}, "consumers": {"type": "array", "items": {"type": "object", "properties": {"queue": {"type": "string", "description": "The name of the queue from which this consumer should consume."}, "type": {"type": "string", "description": "The consumer type, e.g., worker, http-pull, r2-bucket, etc. De<PERSON><PERSON> is worker."}, "max_batch_size": {"type": "number", "description": "The maximum number of messages per batch"}, "max_batch_timeout": {"type": "number", "description": "The maximum number of seconds to wait to fill a batch with messages."}, "max_retries": {"type": "number", "description": "The maximum number of retries for each message."}, "dead_letter_queue": {"type": "string", "description": "The queue to send messages that failed to be consumed."}, "max_concurrency": {"type": ["number", "null"], "description": "The maximum number of concurrent consumer Worker invocations. Leaving this unset will allow your consumer to scale to the maximum concurrency needed to keep up with the message backlog."}, "visibility_timeout_ms": {"type": "number", "description": "The number of milliseconds to wait for pulled messages to become visible again"}, "retry_delay": {"type": "number", "description": "The number of seconds to wait before retrying a message"}}, "required": ["queue"], "additionalProperties": false}, "description": "Consumer configuration"}}, "additionalProperties": false, "description": "Specifies Queues that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#queues", "default": {"consumers": [], "producers": []}}, "r2_buckets": {"type": "array", "items": {"type": "object", "properties": {"binding": {"type": "string", "description": "The binding name used to refer to the R2 bucket in the Worker."}, "bucket_name": {"type": "string", "description": "The name of this R2 bucket at the edge."}, "preview_bucket_name": {"type": "string", "description": "The preview name of this R2 bucket at the edge."}, "jurisdiction": {"type": "string", "description": "The jurisdiction that the bucket exists in. Default if not present."}, "experimental_remote": {"type": "boolean", "description": "Whether the R2 bucket should be remote or not (only available under `--x-remote-bindings`)"}}, "required": ["binding"], "additionalProperties": false}, "description": "Specifies R2 buckets that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#r2-buckets", "default": []}, "d1_databases": {"type": "array", "items": {"type": "object", "properties": {"binding": {"type": "string", "description": "The binding name used to refer to the D1 database in the Worker."}, "database_name": {"type": "string", "description": "The name of this D1 database."}, "database_id": {"type": "string", "description": "The UUID of this D1 database (not required)."}, "preview_database_id": {"type": "string", "description": "The UUID of this D1 database for Wrangler Dev (if specified)."}, "migrations_table": {"type": "string", "description": "The name of the migrations table for this D1 database (defaults to 'd1_migrations')."}, "migrations_dir": {"type": "string", "description": "The path to the directory of migrations for this D1 database (defaults to './migrations')."}, "database_internal_env": {"type": "string", "description": "Internal use only."}, "experimental_remote": {"type": "boolean", "description": "Whether the D1 database should be remote or not (only available under `--x-remote-bindings`)"}}, "required": ["binding"], "additionalProperties": false}, "description": "Specifies D1 databases that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#d1-databases", "default": []}, "vectorize": {"type": "array", "items": {"type": "object", "properties": {"binding": {"type": "string", "description": "The binding name used to refer to the Vectorize index in the Worker."}, "index_name": {"type": "string", "description": "The name of the index."}, "experimental_remote": {"type": "boolean", "description": "Whether the Vectorize index should be remote or not (only available under `--x-remote-bindings`)"}}, "required": ["binding", "index_name"], "additionalProperties": false}, "description": "Specifies Vectorize indexes that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#vectorize-indexes", "default": []}, "hyperdrive": {"type": "array", "items": {"type": "object", "properties": {"binding": {"type": "string", "description": "The binding name used to refer to the project in the Worker."}, "id": {"type": "string", "description": "The id of the database."}, "localConnectionString": {"type": "string", "description": "The local database connection string for `wrangler dev`"}}, "required": ["binding", "id"], "additionalProperties": false}, "description": "Specifies Hyperdrive configs that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#hyperdrive", "default": []}, "services": {"type": "array", "items": {"type": "object", "properties": {"binding": {"type": "string", "description": "The binding name used to refer to the bound service."}, "service": {"type": "string", "description": "The name of the service."}, "environment": {"type": "string", "description": "The environment of the service (e.g. production, staging, etc)."}, "entrypoint": {"type": "string", "description": "Optionally, the entrypoint (named export) of the service to bind to."}, "props": {"type": "object", "additionalProperties": {}, "description": "Optional properties that will be made available to the service via ctx.props."}, "experimental_remote": {"type": "boolean", "description": "Whether the service binding should be remote or not (only available under `--x-remote-bindings`)"}}, "required": ["binding", "service"], "additionalProperties": false}, "description": "Specifies service bindings (Worker-to-Worker) that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#service-bindings", "default": []}, "analytics_engine_datasets": {"type": "array", "items": {"type": "object", "properties": {"binding": {"type": "string", "description": "The binding name used to refer to the dataset in the Worker."}, "dataset": {"type": "string", "description": "The name of this dataset to write to."}}, "required": ["binding"], "additionalProperties": false}, "description": "Specifies analytics engine datasets that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#analytics-engine-datasets", "default": []}, "browser": {"type": "object", "properties": {"binding": {"type": "string"}, "experimental_remote": {"type": "boolean", "description": "Whether the Browser binding should be remote or not (only available under `--x-remote-bindings`)"}}, "required": ["binding"], "additionalProperties": false, "description": "A browser that will be usable from the Worker.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#browser-rendering", "default": {}}, "ai": {"type": "object", "properties": {"binding": {"type": "string"}, "staging": {"type": "boolean"}, "experimental_remote": {"type": "boolean", "description": "Whether the AI binding should be remote or not (only available under `--x-remote-bindings`)"}}, "required": ["binding"], "additionalProperties": false, "description": "Binding to the AI project.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#workers-ai", "default": {}}, "images": {"type": "object", "properties": {"binding": {"type": "string"}, "experimental_remote": {"type": "boolean", "description": "Whether the Images binding should be remote or not (only available under `--x-remote-bindings`)"}}, "required": ["binding"], "additionalProperties": false, "description": "Binding to Cloudflare Images\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#images", "default": {}}, "version_metadata": {"type": "object", "properties": {"binding": {"type": "string"}}, "required": ["binding"], "additionalProperties": false, "description": "Binding to the Worker Version's metadata"}, "unsafe": {"type": "object", "properties": {"bindings": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}}, "required": ["name", "type"], "additionalProperties": {}}, "description": "A set of bindings that should be put into a Worker's upload metadata without changes. These can be used to implement bindings for features that haven't released and aren't supported directly by wrangler or miniflare."}, "metadata": {"type": "object", "additionalProperties": {}, "description": "Arbitrary key/value pairs that will be included in the uploaded metadata.  Values specified here will always be applied to metadata last, so can add new or override existing fields."}, "capnp": {"anyOf": [{"type": "object", "properties": {"base_path": {"type": "string"}, "source_schemas": {"type": "array", "items": {"type": "string"}}}, "required": ["base_path", "source_schemas"], "additionalProperties": false}, {"type": "object", "properties": {"compiled_schema": {"type": "string"}}, "required": ["compiled_schema"], "additionalProperties": false}], "description": "Used for internal capnp uploads for the Workers runtime"}}, "additionalProperties": false, "description": "\"Unsafe\" tables for features that aren't directly supported by wrangler.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "default": {}}, "mtls_certificates": {"type": "array", "items": {"type": "object", "properties": {"binding": {"type": "string", "description": "The binding name used to refer to the certificate in the Worker"}, "certificate_id": {"type": "string", "description": "The uuid of the uploaded mTLS certificate"}, "experimental_remote": {"type": "boolean", "description": "Whether the mtls fetcher should be remote or not (only available under `--x-remote-bindings`)"}}, "required": ["binding", "certificate_id"], "additionalProperties": false}, "description": "Specifies a list of mTLS certificates that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#mtls-certificates", "default": []}, "tail_consumers": {"type": "array", "items": {"$ref": "#/definitions/TailConsumer"}, "description": "Specifies a list of Tail Workers that are bound to this Worker environment\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "default": []}, "dispatch_namespaces": {"type": "array", "items": {"type": "object", "properties": {"binding": {"type": "string", "description": "The binding name used to refer to the bound service."}, "namespace": {"type": "string", "description": "The namespace to bind to."}, "outbound": {"$ref": "#/definitions/DispatchNamespaceOutbound", "description": "Details about the outbound Worker which will handle outbound requests from your namespace"}, "experimental_remote": {"type": "boolean", "description": "Whether the Dispatch Namespace should be remote or not (only available under `--x-remote-bindings`)"}}, "required": ["binding", "namespace"], "additionalProperties": false}, "description": "Specifies namespace bindings that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.\n\nFor reference, see https://developers.cloudflare.com/workers/wrangler/configuration/#dispatch-namespace-bindings-workers-for-platforms", "default": []}, "pipelines": {"type": "array", "items": {"type": "object", "properties": {"binding": {"type": "string", "description": "The binding name used to refer to the bound service."}, "pipeline": {"type": "string", "description": "Name of the Pipeline to bind"}}, "required": ["binding", "pipeline"], "additionalProperties": false}, "description": "Specifies list of Pipelines bound to this Worker environment\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "default": []}, "secrets_store_secrets": {"type": "array", "items": {"type": "object", "properties": {"binding": {"type": "string", "description": "The binding name used to refer to the bound service."}, "store_id": {"type": "string", "description": "Id of the secret store"}, "secret_name": {"type": "string", "description": "Name of the secret"}}, "required": ["binding", "store_id", "secret_name"], "additionalProperties": false}, "description": "Specifies Secret Store bindings that are bound to this Worker environment.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "default": []}, "unsafe_hello_world": {"type": "array", "items": {"type": "object", "properties": {"binding": {"type": "string", "description": "The binding name used to refer to the bound service."}, "enable_timer": {"type": "boolean", "description": "Whether the timer is enabled"}}, "required": ["binding"], "additionalProperties": false}, "description": "**DO NOT USE**. Hello World Binding Config to serve as an explanatory example.\n\nNOTE: This field is not automatically inherited from the top level environment, and so must be specified in every named environment.", "default": []}}, "additionalProperties": false, "description": "The raw environment configuration that we read from the config file.\n\nAll the properties are optional, and will be replaced with defaults in the configuration that is used in the rest of the codebase."}, "Route": {"anyOf": [{"type": "string"}, {"$ref": "#/definitions/ZoneIdRoute"}, {"$ref": "#/definitions/ZoneNameRoute"}, {"$ref": "#/definitions/CustomDomainRoute"}]}, "ZoneIdRoute": {"type": "object", "properties": {"pattern": {"type": "string"}, "zone_id": {"type": "string"}, "custom_domain": {"type": "boolean"}}, "required": ["pattern", "zone_id"], "additionalProperties": false}, "ZoneNameRoute": {"type": "object", "properties": {"pattern": {"type": "string"}, "zone_name": {"type": "string"}, "custom_domain": {"type": "boolean"}}, "required": ["pattern", "zone_name"], "additionalProperties": false}, "CustomDomainRoute": {"type": "object", "properties": {"pattern": {"type": "string"}, "custom_domain": {"type": "boolean"}}, "required": ["pattern", "custom_domain"], "additionalProperties": false}, "DurableObjectMigration": {"type": "object", "properties": {"tag": {"type": "string", "description": "A unique identifier for this migration."}, "new_classes": {"type": "array", "items": {"type": "string"}, "description": "The new Durable Objects being defined."}, "new_sqlite_classes": {"type": "array", "items": {"type": "string"}, "description": "The new SQLite Durable Objects being defined."}, "renamed_classes": {"type": "array", "items": {"type": "object", "properties": {"from": {"type": "string"}, "to": {"type": "string"}}, "required": ["from", "to"], "additionalProperties": false}, "description": "The Durable Objects being renamed."}, "deleted_classes": {"type": "array", "items": {"type": "string"}, "description": "The Durable Objects being removed."}}, "required": ["tag"], "additionalProperties": false, "description": "Configuration in wrangler for Durable Object Migrations"}, "UserLimits": {"type": "object", "properties": {"cpu_ms": {"type": "number", "description": "Maximum allowed CPU time for a Worker's invocation in milliseconds"}}, "required": ["cpu_ms"], "additionalProperties": false}, "Rule": {"type": "object", "properties": {"type": {"$ref": "#/definitions/ConfigModuleRuleType"}, "globs": {"type": "array", "items": {"type": "string"}}, "fallthrough": {"type": "boolean"}}, "required": ["type", "globs"], "additionalProperties": false, "description": "A bundling resolver rule, defining the modules type for paths that match the specified globs."}, "ConfigModuleRuleType": {"type": "string", "enum": ["ESModule", "CommonJS", "CompiledWasm", "Text", "Data", "PythonModule", "PythonRequirement"], "description": "The possible types for a `Rule`."}, "Assets": {"type": "object", "properties": {"directory": {"type": "string", "description": "Absolute path to assets directory"}, "binding": {"type": "string", "description": "Name of `env` binding property in the User Worker."}, "html_handling": {"type": "string", "enum": ["auto-trailing-slash", "force-trailing-slash", "drop-trailing-slash", "none"], "description": "How to handle HTML requests."}, "not_found_handling": {"type": "string", "enum": ["single-page-application", "404-page", "none"], "description": "How to handle requests that do not match an asset."}, "run_worker_first": {"anyOf": [{"type": "array", "items": {"type": "string"}}, {"type": "boolean"}], "description": "Matches will be routed to the User Worker, and matches to negative rules will go to the Asset Worker.\n\nCan also be `true`, indicating that every request should be routed to the User Worker."}}, "additionalProperties": false}, "Observability": {"type": "object", "properties": {"enabled": {"type": "boolean", "description": "If observability is enabled for this Worker"}, "head_sampling_rate": {"type": "number", "description": "The sampling rate"}, "logs": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "head_sampling_rate": {"type": "number", "description": "The sampling rate"}, "invocation_logs": {"type": "boolean", "description": "Set to false to disable invocation logs"}}, "additionalProperties": false}}, "additionalProperties": false}, "Json": {"anyOf": [{"$ref": "#/definitions/Literal"}, {"type": "object", "additionalProperties": {"$ref": "#/definitions/Json"}}, {"type": "array", "items": {"$ref": "#/definitions/Json"}}]}, "Literal": {"$ref": "#/definitions/TypeOf%3CZodUnion%3C%5Bdef-class-1315922706-6501-8772-1315922706-0-54395%2Cdef-class-1315922706-9299-10989-1315922706-0-54395%2Cdef-class-1315922706-12937-13365-1315922706-0-54395%2Cdef-class-1315922706-15083-15273-1315922706-0-54395%5D%3E%3E"}, "TypeOf<ZodUnion<[def-class-1315922706-6501-8772-1315922706-0-54395,def-class-1315922706-9299-10989-1315922706-0-54395,def-class-1315922706-12937-13365-1315922706-0-54395,def-class-1315922706-15083-15273-1315922706-0-54395]>>": {"type": ["string", "number", "boolean", "null"]}, "DurableObjectBindings": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the binding used to refer to the Durable Object"}, "class_name": {"type": "string", "description": "The exported class name of the Durable Object"}, "script_name": {"type": "string", "description": "The script where the Durable Object is defined (if it's external to this Worker)"}, "environment": {"type": "string", "description": "The service environment of the script_name to bind to"}}, "required": ["name", "class_name"], "additionalProperties": false}}, "WorkflowBinding": {"type": "object", "properties": {"binding": {"type": "string", "description": "The name of the binding used to refer to the Workflow"}, "name": {"type": "string", "description": "The name of the Workflow"}, "class_name": {"type": "string", "description": "The exported class name of the Workflow"}, "script_name": {"type": "string", "description": "The script where the Workflow is defined (if it's external to this Worker)"}, "experimental_remote": {"type": "boolean", "description": "Whether the Workflow should be remote or not (only available under `--x-remote-bindings`)"}}, "required": ["binding", "name", "class_name"], "additionalProperties": false}, "CloudchamberConfig": {"type": "object", "properties": {"image": {"type": "string"}, "location": {"type": "string"}, "instance_type": {"type": "string", "enum": ["dev", "basic", "standard"]}, "vcpu": {"type": "number"}, "memory": {"type": "string"}, "ipv4": {"type": "boolean"}}, "additionalProperties": false, "description": "Configuration in wrangler for Cloudchamber"}, "ContainerApp": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the application"}, "max_instances": {"type": "number", "description": "Number of maximum application instances."}, "image": {"type": "string", "description": "The path to a Dockerfile, or an image URI for the Cloudflare registry."}, "image_build_context": {"type": "string", "description": "Build context of the application."}, "image_vars": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Image variables to be passed along the image at build time."}, "class_name": {"type": "string", "description": "The class name of the Durable Object the container is connected to."}, "scheduling_policy": {"type": "string", "enum": ["regional", "moon", "default"], "description": "The scheduling policy of the application", "default": "default"}, "instance_type": {"type": "string", "enum": ["dev", "basic", "standard"], "description": "The instance type to be used for the container. This sets preconfigured options for vcpu and memory"}, "rollout_step_percentage": {"type": "number", "description": "How a rollout should be done, defining the size of it", "default": 25}, "rollout_kind": {"type": "string", "enum": ["full_auto", "none", "full_manual"], "description": "How a rollout should be created. It supports the following modes:  - full_auto: The container application will be rolled out fully automatically.  - none: The container application won't have a roll out or update.  - manual: The container application will be rollout fully by manually actioning progress steps.", "default": "full_auto"}}, "required": ["image", "class_name"], "additionalProperties": false, "description": "Configuration for a container application"}, "TailConsumer": {"type": "object", "properties": {"service": {"type": "string", "description": "The name of the service tail events will be forwarded to."}, "environment": {"type": "string", "description": "(Optional) The environment of the service."}}, "required": ["service"], "additionalProperties": false}, "DispatchNamespaceOutbound": {"type": "object", "properties": {"service": {"type": "string", "description": "Name of the service handling the outbound requests"}, "environment": {"type": "string", "description": "(Optional) Name of the environment handling the outbound requests."}, "parameters": {"type": "array", "items": {"type": "string"}, "description": "(Optional) List of parameter names, for sending context from your dispatch Worker to the outbound handler"}}, "required": ["service"], "additionalProperties": false}, "RawDevConfig": {"type": "object", "properties": {"ip": {"type": "string", "description": "IP address for the local dev server to listen on,", "default": "localhost"}, "port": {"type": "number", "description": "Port for the local dev server to listen on", "default": 8787}, "inspector_port": {"type": "number", "description": "Port for the local dev server's inspector to listen on", "default": 9229}, "local_protocol": {"type": "string", "enum": ["http", "https"], "description": "Protocol that local wrangler dev server listens to requests on.", "default": "http"}, "upstream_protocol": {"type": "string", "enum": ["https", "http"], "description": "Protocol that wrangler dev forwards requests on\n\nSetting this to `http` is not currently implemented for remote mode. See https://github.com/cloudflare/workers-sdk/issues/583", "default": "https"}, "host": {"type": "string", "description": "Host to forward requests to, defaults to the host of the first route of project"}, "enable_containers": {"type": "boolean", "description": "When developing, whether to build and connect to containers. This requires a Docker daemon to be running. Defaults to `true`.", "default": true}, "container_engine": {"$ref": "#/definitions/ContainerEngine", "description": "Either the Docker unix socket i.e. `unix:///var/run/docker.sock` or a full configuration. Note that windows is only supported via WSL at the moment"}}, "additionalProperties": false}, "ContainerEngine": {"anyOf": [{"type": "object", "properties": {"localDocker": {"$ref": "#/definitions/DockerConfiguration"}}, "required": ["localDocker"], "additionalProperties": false}, {"type": "string"}]}, "DockerConfiguration": {"type": "object", "properties": {"socketPath": {"type": "string", "description": "Socket used by miniflare to communicate with <PERSON><PERSON>"}}, "required": ["socketPath"], "additionalProperties": false}}, "allowTrailingCommas": true}